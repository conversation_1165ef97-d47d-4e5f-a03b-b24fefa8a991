import { useEffect, useState } from "react";
import { UserContext } from "../contexts/UserContext";
import axiosInstance from "../axios";
import { disconnectSocket, reconnectSocket, getSocket } from "../socket";
import { jwtDecode } from "jwt-decode";

export const UserProvider = ({ children }) => {
    const [userFetched, setUserFetched] = useState(false);
    const [user, setUser] = useState(null);
    const [sessionExpired, setSessionExpired] = useState(false);

    useEffect(() => {
        const usersChangeListener = (data) => {
            if (data._id === user._id) fetchUser();
        };

        const rolesChangeListener = (data) => {
            if (data.role_id === user.role_id) fetchUser();
        };

        const socket = getSocket();

        socket.on("users/changed", usersChangeListener);
        socket.on("roles/changed", rolesChangeListener);

        return () => {
            socket.off("users/changed", usersChangeListener);
            socket.off("roles/changed", rolesChangeListener);
        };
    }, [user]);

    const login = async ({ username, password }) => {
        return new Promise((resolve, reject) => {
            axiosInstance
                .get("/users/auth", { headers: { Authorization: `Basic ${btoa(`${username}:${password}`)}` }, meta: { showSnackbar: false } })
                .then((res) => {
                    localStorage.setItem("jwt_token", res.data.jwt_token);
                    reconnectSocket();
                    fetchUser()
                        .then((user) => resolve(user))
                        .catch(reject);
                })
                .catch(reject);
        });
    };

    const logout = (callback) => {
        localStorage.removeItem("jwt_token");
        disconnectSocket();
        setTimeout(() => {
            setUser(null);
            callback && callback();
        }, 500);
    };

    const fetchUser = () => {
        return new Promise((resolve, reject) => {
            const jwt_token = localStorage.getItem("jwt_token");
            if (!jwt_token) {
                setUserFetched(true);
                return reject("No token found");
            }

            const decodedToken = jwtDecode(jwt_token);
            if (new Date(decodedToken.exp * 1000).getTime() < new Date().getTime()) {
                setUserFetched(true);
                return reject("Your session has expired");
            }

            axiosInstance
                .get("/users/user", { meta: { showSnackbar: false } })
                .then((res) => {
                    const user = res.data;
                    user.hasPermissions = function (permissionIds, op = "AND") {
                        return op === "AND"
                            ? permissionIds.every((p_id) => this.permissions.find((p) => p.permission_id === p_id))
                            : permissionIds.some((p_id) => this.permissions.find((p) => p.permission_id === p_id));
                    };
                    setUser(user);
                    setUserFetched(true);
                    resolve(user);
                })
                .catch((err) => {
                    localStorage.removeItem("jwt_token");
                    reject(err);
                });
        });
    };

    return (
        <UserContext.Provider value={{ user, userFetched, login, logout, fetchUser, sessionExpired, setSessionExpired }}>
            {children}
        </UserContext.Provider>
    );
};
