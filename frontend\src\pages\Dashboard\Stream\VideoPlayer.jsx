import { useEffect, useRef, useState } from "react";
import { MediaPlayer as DashJSMediaPlayer } from "dashjs";
import { Box, Grid, IconButton, Slider, Tooltip, Typography, alpha, CircularProgress } from "@mui/material";
import { Fullscreen, Pause, PlayArrow } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import { useLocation } from "react-router-dom";
import SensorDetails from "./SensorDetails";
import dayjs from "dayjs";
import theme from "../../../theme";
import { userValues } from "../../../utils.js";
import { useUser } from "../../../hooks/UserHook.jsx";

const seaTheme = ({ isMobile }) => {
    return {
        playerWrapper: {
            position: "relative",
            backgroundColor: "#000000",
            overflow: "hidden",
            width: "100%",
            height: "100%",
        },
        controlsWrapper: {
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            background: "#FFFFFF4D",
            display: "flex",
            flexDirection: "column",
            height: "32px",
        },
        playButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        progressBar: {
            flexGrow: 1,
            height: isMobile ? "auto" : "100%",
        },
        sliderStyles: {
            color: theme.palette.custom.mainBlue,
            padding: "4px 0",
            height: "100%",
            "& .MuiSlider-thumb": {
                width: 8,
                height: 32,
                borderRadius: 0,
                backgroundColor: "#FFFFFF",
                transform: isMobile ? "" : "translate(-50%, -65%)",
                "&:hover, &.Mui-focusVisible": {
                    boxShadow: "none",
                },
            },
            "& .MuiSlider-track": {
                height: isMobile ? "80%" : "100%",
                borderRadius: 0,
                color: theme.palette.custom.mainBlue,
            },
            "& .MuiSlider-rail": {
                height: "100%",
                borderRadius: 0,
                opacity: 0.28,
                backgroundColor: "#FFFFFF4D",
            },
            "& .MuiSlider-mark": { height: "100%", width: 4 },
        },
        fullscreenButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        timeDisplay: {
            fontSize: "0.8rem",
            color: "white",
            marginLeft: 1,
            marginRight: 1,
        },
        loadingIndicator: {
            position: "absolute",
            top: "42%",
            left: "50%",
            color: theme.palette.custom.mainBlue,
        },
        centerPlayButton: {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: alpha(theme.palette.custom.mainBlue, 0.7),
            color: "white",
            width: 80,
            height: 80,
            "&:hover": {
                backgroundColor: theme.palette.custom.mainBlue,
            },
            boxShadow: "0px 0px 15px rgba(0, 0, 0, 0.3)",
        },
    };
};

const VideoPlayer = ({
    streamUrl,
    setStreamUrl,
    streamMode,
    totalDuration,
    playBack,
    setPlayBack,
    setScrubBarSlotInterval,
    selectedStream,
    referenceTime,
    // handleReplay,
    view = "Single",
    sharedPlayback,
    lockSlider,
    hlsUrlError,
    marks,
}) => {
    const { isMobile, isTabActive, timezone } = useApp();
    const { user } = useUser();
    const videoNode = useRef(null);
    const videoContainerRef = useRef(null);
    const player = useRef(null);
    const [isPlayingVideo, setIsPlayingVideo] = useState(true);
    const [isFullscreen, setIsFullScreen] = useState(false);
    const [hoverTime, setHoverTime] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const { pathname } = useLocation();
    const scrubBarRef = useRef();
    const allowDelay = 4;

    const resetPlayer = (resetHlsUrl = true) => {
        if (player.current) {
            player.current.reset();
            player.current = null;
            if (resetHlsUrl) setStreamUrl("");
        }
    };
    const seaThemeStyles = seaTheme({ isMobile });
    const initializePlayer = () => {
        if (videoNode.current && streamUrl) {
            setIsLoading(true);
            player.current = DashJSMediaPlayer().create();
            player.current.initialize(videoNode.current, streamUrl, true);
            player.current.setAutoPlay(true);
            player.current.updateSettings({
                streaming: {
                    delay: {
                        liveDelay: allowDelay,
                    },
                    liveCatchup: {
                        maxDrift: 0,
                        playbackRate: {
                            max: 1,
                            min: -0.5,
                        },
                    },
                },
            });

            player.current.on(DashJSMediaPlayer.events.PLAYBACK_STARTED, () => {
                setTimeout(() => {
                    setIsPlayingVideo(true);
                }, 200);
            });

            player.current.on(DashJSMediaPlayer.events.PLAYBACK_PAUSED, () => {
                setIsPlayingVideo(false);
            });

            player.current.on(DashJSMediaPlayer.events.BUFFER_LEVEL_STATE_CHANGED, function (e) {
                if (e.state === "bufferLoaded") {
                    setIsLoading(false);
                } else if (e.state === "bufferStalled") {
                    setIsLoading(true);
                } else if (e.state === "bufferEmpty") {
                    setIsLoading(true);
                }
            });

            // player.current.on(DashJSMediaPlayer.events.ERROR, (error) => {
            //     if (error.error.code === 25 || error.error.code === 27) {
            //         handleReplay({ title: "Last 1 Hour", interval: 60, time: 60, tag: "1H" });
            //     }
            // });
        }
    };

    const handleHover = (event) => {
        const scrubBar = scrubBarRef.current;
        if (!scrubBar) return;

        const rect = scrubBar.getBoundingClientRect();
        const relativeX = event.clientX - rect.left;
        const clampedX = Math.max(0, Math.min(relativeX, rect.width));
        const hoveredTime = (clampedX / rect.width) * totalDuration;

        setHoverTime(hoveredTime);
    };

    const handlePause = () => {
        if (lockSlider) return;
        if (player.current) {
            if (!player.current.isPaused()) {
                player.current.pause();
            } else {
                player.current.play();
            }
            setIsPlayingVideo(!isPlayingVideo);
        }
    };

    const handleFullscreen = () => {
        if (videoContainerRef.current) {
            if (!document.fullscreenElement) {
                videoContainerRef.current.requestFullscreen();
                setIsFullScreen(true);
            } else {
                document.exitFullscreen();
                setIsFullScreen(false);
            }
        }
    };

    const formatTime = (user, timeInSeconds) => {
        const remainingTimeInSeconds = totalDuration - timeInSeconds;
        const days = Math.floor(remainingTimeInSeconds / 86400);
        const hours = Math.floor((remainingTimeInSeconds % 86400) / 3600);
        const minutes = Math.floor((remainingTimeInSeconds % 3600) / 60);
        const seconds = Math.floor(remainingTimeInSeconds % 60);

        const startDate = dayjs(referenceTime.current - totalDuration * 1000).tz(timezone);
        const pastDate = startDate.add(timeInSeconds, "second");
        const formattedDate = pastDate.format(userValues.dateTimeFormat(user));
        const timeString =
            days > 0
                ? `- ${days}D ${hours.toString().padStart(2, "0")}H ${minutes.toString().padStart(2, "0")}M ${seconds.toString().padStart(2, "0")}S`
                : `- ${hours.toString().padStart(2, "0")}H ${minutes.toString().padStart(2, "0")}M ${seconds.toString().padStart(2, "0")}S`;
        return (
            <Grid sx={{ textAlign: "center" }}>
                <Grid>{timeString}</Grid>
                <Grid>({formattedDate})</Grid>
            </Grid>
        );
    };

    const formatTimePart = (part) => part.toString().padStart(2, "0");

    const formatTimeStr = (millis) => {
        const days = Math.floor(millis / 86400);
        const hours = Math.floor((millis % 86400) / 3600);
        const minutes = Math.floor((millis % 3600) / 60);
        const seconds = Math.floor(millis % 60);

        let res = `${formatTimePart(minutes)}:${formatTimePart(seconds)}`;

        if (hours > 0) {
            res = `${formatTimePart(hours)}:${res}`;
        }

        if (days > 0) {
            res = `${formatTimePart(days)}:${res}`;
        }

        return res;
    };

    // Format current time in MM:SS format - updated to reflect actual position in the buffer
    const formatCurrentTime = () => {
        const actualPosition = playBack.offset + playBack.currentVideoPlayTime;
        return formatTimeStr(actualPosition);
    };

    // Format total duration in MM:SS format
    const formatDuration = () => {
        return formatTimeStr(totalDuration);
    };

    const handleScrub = (event, newValue) => {
        const video = videoNode.current;
        if (video) {
            const clickedTime = (newValue / 100) * totalDuration;
            setPlayBack(() => ({
                offset: clickedTime,
                currentVideoPlayTime: 0,
                latency: 0,
            }));
            setScrubBarSlotInterval(totalDuration / 60 - clickedTime / 60);
        }
    };

    const handleTimeUpdate = () => {
        const video = videoNode.current;
        if (video) {
            const currentTime = video.currentTime;
            setPlayBack((prev) => ({
                offset: prev.offset,
                currentVideoPlayTime: currentTime,
                latency: player.current && (player.current.getCurrentLiveLatency?.() || player.current.getLiveLatency?.()),
            }));
        }
    };

    useEffect(() => {
        if (hlsUrlError && hlsUrlError === "No streams found in the specified timestamp range.") {
            setIsLoading(false);
            if (player.current) {
                player.current.reset();
                player.current = null;
            }
        }
    }, [hlsUrlError]);
    useEffect(() => {
        if (player.current && streamMode === "LIVE") {
            const intervalId = setInterval(() => {
                if (player.current.isPaused()) return;
                const currentLatency = player.current.getCurrentLiveLatency?.() || player.current.getLiveLatency?.();

                if (currentLatency && currentLatency > allowDelay) {
                    player.current.seek(player.current.duration() - (currentLatency - allowDelay));
                }
            }, 5000); // Every 5 seconds

            return () => clearInterval(intervalId);
        }
    }, [player.current, streamMode]);

    useEffect(() => {
        const video = videoNode.current;
        if (video) {
            video.addEventListener("timeupdate", handleTimeUpdate);
        }
        return () => {
            if (video) {
                video.removeEventListener("timeupdate", handleTimeUpdate);
            }
        };
    }, [streamMode]);

    useEffect(() => {
        if (!pathname.includes("/stream") || !isTabActive) {
            resetPlayer(false);
            return;
        }
        if (streamUrl && videoNode.current) {
            if (player.current) {
                // console.log("Attaching source to existing player");
                setIsLoading(true);
                player.current.reset();
                player.current.initialize(videoNode.current, streamUrl, true);
                player.current.setAutoPlay(true);
            } else {
                initializePlayer();
            }
        }
    }, [streamUrl, videoNode.current, pathname, isTabActive]);

    useEffect(() => {
        const playerCurr = player.current;

        return () => {
            if (playerCurr) {
                playerCurr.reset();
                player.current = null;
            }
        };
    }, []);

    useEffect(() => {
        if (streamUrl && videoNode.current) {
            if (player.current) {
                // Reinitialize the player with the new stream URL
                player.current.attachSource(streamUrl);
                player.current.setAutoPlay(true);
            } else {
                initializePlayer();
            }
        }
    }, [streamUrl]);

    useEffect(() => {
        if (sharedPlayback) {
            if (sharedPlayback.isPaused) {
                player.current?.pause();
            } else {
                player.current?.play();
            }
        }
    }, [sharedPlayback]);

    // Calculate the current position as a percentage for the slider
    const currentPositionPercent = ((playBack.offset + playBack.currentVideoPlayTime) / totalDuration) * 100;
    const endTime = Date.now();
    const startTime = endTime - totalDuration * 1000;
    const computedMarks = marks?.map((mark) => {
        const artifactTime = new Date(mark.timestamp).getTime();
        const secondsFromStart = (artifactTime - startTime) / 1000;
        // Map seconds into percentage relative to totalDuration (0 to 100)
        const percentageValue = (secondsFromStart / totalDuration) * 100;
        return { value: percentageValue };
        // label: mark.count.toString()
    });

    return (
        <Grid minHeight={{ xs: 300, lg: "auto" }} height={view === "Mosaic" && isMobile ? "auto" : "100%"} ref={videoContainerRef} size="grow">
            <Box sx={seaThemeStyles.playerWrapper} onDoubleClick={handleFullscreen}>
                <video
                    ref={videoNode}
                    id="video"
                    preload="auto"
                    autoPlay
                    muted
                    style={{
                        width: "100%",
                        height: view === "Mosaic" ? "auto" : isFullscreen ? "100%" : isMobile ? "95%" : "80vh",
                        // objectFit: "cover",
                    }}
                    onClick={handlePause}
                />
                {hlsUrlError && hlsUrlError === "No streams found in the specified timestamp range." ? (
                    <Box
                        sx={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            width: "fit-content",
                            height: "fit-content",
                            margin: "auto",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <Typography sx={{ textAlign: "center", color: "white", fontSize: "1.5rem" }}>{hlsUrlError}</Typography>
                    </Box>
                ) : (
                    isLoading && (
                        <Box sx={seaThemeStyles.loadingIndicator}>
                            <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                        </Box>
                    )
                )}

                {!isPlayingVideo && !isLoading && (
                    <IconButton
                        sx={{
                            ...seaThemeStyles.centerPlayButton,
                            opacity: 1,
                            transition: "opacity 0.3s",
                        }}
                        onClick={handlePause}
                        aria-label="play video"
                    >
                        <PlayArrow sx={{ fontSize: 32 }} />
                    </IconButton>
                )}

                <Box
                    sx={{
                        ...seaThemeStyles.controlsWrapper,
                        height: "32px",
                        display: lockSlider ? "none" : "flex",
                    }}
                >
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            width: "100%",
                            height: "100%",
                        }}
                    >
                        <IconButton onClick={handlePause} sx={seaThemeStyles.playButton}>
                            {isPlayingVideo ? <Pause sx={{ fontSize: 20 }} /> : <PlayArrow sx={{ fontSize: 20 }} />}
                        </IconButton>

                        <Box
                            ref={scrubBarRef}
                            sx={{
                                ...seaThemeStyles.progressBar,
                                flexGrow: 1,
                                mx: 0,
                                // height: "100%",
                            }}
                            onMouseMove={streamMode === "ON_DEMAND" ? handleHover : undefined}
                            onMouseLeave={() => setHoverTime(null)}
                        >
                            {streamMode === "ON_DEMAND" && (
                                <Tooltip
                                    open={hoverTime !== null}
                                    title={formatTime(user, hoverTime || 0)}
                                    placement="top"
                                    arrow
                                    followCursor
                                    PopperProps={{
                                        container: videoContainerRef.current,
                                    }}
                                >
                                    <Slider
                                        value={currentPositionPercent}
                                        onChange={handleScrub}
                                        sx={seaThemeStyles.sliderStyles}
                                        marks={computedMarks}
                                    />
                                </Tooltip>
                            )}
                        </Box>

                        {(streamMode === "LIVE" || streamMode === "ON_DEMAND") && (
                            <Box sx={{ display: "flex", justifyContent: "center" }}>
                                {streamMode === "LIVE" && (
                                    <Typography
                                        sx={{
                                            ...seaThemeStyles.timeDisplay,
                                            color: theme.palette.custom.mainBlue,
                                            fontWeight: "bold",
                                        }}
                                    >
                                        LIVE
                                    </Typography>
                                )}

                                {streamMode === "ON_DEMAND" && (
                                    <Typography sx={seaThemeStyles.timeDisplay}>
                                        {formatCurrentTime()} / {formatDuration()}
                                    </Typography>
                                )}
                            </Box>
                        )}

                        <IconButton onClick={handleFullscreen} sx={seaThemeStyles.fullscreenButton}>
                            <Fullscreen sx={{ fontSize: 20 }} />
                        </IconButton>
                    </Box>
                </Box>
            </Box>
            <Box position={"absolute"} bottom={45} right={10} display={{ xs: "none", lg: view === "Mosaic" ? "none" : "block" }}>
                <Grid
                    container
                    sx={{
                        backgroundColor: (theme) => alpha(theme.palette.primary.light, 0.5),
                        borderRadius: "20px",
                        width: "auto",
                        padding: "25px 20px",
                        minWidth: "350px",
                    }}
                >
                    <Grid container width={"100%"} flexDirection={"column"} spacing={1} color={"primary.contrastText"} className="dashboard-step-6">
                        <Grid>
                            <Typography fontSize={"14px"} fontWeight={"600"}>
                                Details
                            </Typography>
                        </Grid>
                        <Grid>
                            <SensorDetails selectedStream={selectedStream} />
                        </Grid>
                    </Grid>
                </Grid>
            </Box>
        </Grid>
    );
};

export default VideoPlayer;
