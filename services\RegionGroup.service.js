const RegionGroup = require("../models/RegionGroup");
const Vessel = require("../models/VesselManagement");
const { isValidObjectId, default: mongoose } = require("mongoose");
const streamService = require("./Stream.service");

class RegionGroupService {
    async fetchAll() {
        const regionGroups = await RegionGroup.aggregate([
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$created_by",
            },
        ]);

        const vessels = await Vessel.find({}, { _id: 1, name: 1, unit_id: 1 });

        const updatedRegionGroups = regionGroups.map((regionGroup) => {
            const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup.vessel_ids?.includes(vessel._id.toString()));

            return {
                ...regionGroup,
                vessels: vesselsInRegionGroup.map((vessel) => ({
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                })),
            };
        });

        return updatedRegionGroups;
    }

    async findById({ id }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");
        const regionGroup = await RegionGroup.aggregate([
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(id),
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$created_by",
            },
        ]);

        if (!regionGroup.length) return null;

        const vessels = await Vessel.find({}, { _id: 1, name: 1, unit_id: 1 });
        const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup[0].vessel_ids.includes(vessel._id.toString()));

        return {
            ...regionGroup[0],
            vessels: vesselsInRegionGroup.map((vessel) => ({
                vessel_id: vessel._id.toString(),
                unit_id: vessel.unit_id,
                name: vessel.name,
            })),
        };
    }

    async create({ name, timezone, vessel_ids, created_by }) {
        const regionGroup = await RegionGroup.create({ name, timezone, unit_ids: [], vessel_ids, created_by });
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async update({ id, name, timezone, vessel_ids }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");

        const data = { name, timezone, vessel_ids };
        Object.keys(data).forEach((key) => {
            if (data[key] === undefined) delete data[key];
        });

        const regionGroup = await RegionGroup.findByIdAndUpdate(id, data, { new: true });
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async delete({ id }) {
        const result = await RegionGroup.findByIdAndDelete(id);
        streamService.resetCache();
        return result;
    }
}

const regionGroupService = new RegionGroupService();

module.exports = regionGroupService;
