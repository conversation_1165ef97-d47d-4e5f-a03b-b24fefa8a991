const express = require("express");
const Region = require("../models/Region");
const { validateError } = require("../utils/functions");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_REGIONS), isAuthenticated, async (req, res) => {
    try {
        const regions = await Region.find();
        res.json(regions);
    } catch (err) {
        validateError(err, res);
    }
});

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Regions
 *   description: Fetch regions
 * components:
 *   schemas:
 *     Region:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the region
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: number
 *           description: Name of the region
 *           example: US East (Ohio)
 *         value:
 *           type: string
 *           description: Region identifier
 *           example: us-east-2
 *         default:
 *           type: boolean
 *           description: Whether it is the main region
 *           example: false
 *           deprecated: true
 *         is_live:
 *           type: boolean
 *           description: Whether this region is in use
 *           example: false
 *         timezone:
 *           type: string
 *           description: Timezone of the region
 *           example: Asia/Shanghai
 */

/**
 * @swagger
 * /regions:
 *   get:
 *     summary: Fetch all regions
 *     description: Rate limited to 10 requests every 5 seconds
 *     tags: [Regions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of regions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Region'
 *       500:
 *         description: Server error
 */
