const mongoose = require("mongoose");
const db = require("../modules/db");

const regionGroupSchema = new mongoose.Schema({
    name: { type: String, required: true, unique: true },
    timezone: { type: String, required: true },
    unit_ids: { type: Array, required: true },
    vessel_ids: { type: Array, required: true },
    created_by: { type: mongoose.Schema.Types.ObjectId, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

const RegionGroup = db.qm.model("RegionGroup", regionGroupSchema, "regions_groups");

module.exports = RegionGroup;
