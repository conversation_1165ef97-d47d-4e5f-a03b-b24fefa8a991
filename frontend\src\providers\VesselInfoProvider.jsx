import { useEffect, useState } from "react";
import { VesselInfoContext } from "../contexts/VesselInfoContext";
import vesselController from "../controllers/Vessel.controller";
import { useUser } from "../hooks/UserHook";
import useGroupRegions from "../hooks/GroupRegionHook";

export const VesselInfoProvider = ({ children }) => {
    const [vesselInfo, setVesselInfo] = useState([]);
    const { user } = useUser();
    const { regions } = useGroupRegions();

    const fetchVesselsInfo = async () => {
        try {
            const payload = {};
            if (regions && regions.length > 0) {
                payload.region_groups = regions.map((r) => r._id).join(",");
            }
            const vessels = await vesselController.fetchAll(payload);
            setVesselInfo(vessels);
            return vessels;
        } catch (err) {
            console.error("An error occurred while fetching vessels in the VesselContext :", err);
        }
    };
    useEffect(() => {
        if (user) {
            fetchVesselsInfo();
        }
    }, [user]);
    return <VesselInfoContext.Provider value={{ vesselInfo, fetchVesselsInfo }}>{children}</VesselInfoContext.Provider>;
};
