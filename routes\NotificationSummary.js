const { default: rateLimit } = require("express-rate-limit");
const express = require("express");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();
const NotificationSummary = require("../models/NotificationSummary");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body, param, query } = require("express-validator");
const { sendEmail } = require("../modules/email");

const { SUMMARY_SUBSCRIPTION_EMAIL_CONTENT } = require("../utils/Email");
const jwt = require("jsonwebtoken");

const { generateUnsubscribeToken, userHasPermissions } = require("../utils/functions");

const EmailDomains = require("../models/EmailDomains");
const { permissions } = require("../utils/permissions");
const User = require("../models/User");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,

    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_SUMMARIES), isAuthenticated, async (req, res) => {
    const page = req.query.page || 1;
    const page_size = req.query.page_size || 10;

    try {
        const totalDocuments = await NotificationSummary.countDocuments({ created_by: req.user._id });
        const totalPages = Math.ceil(totalDocuments / page_size);
        const notificationSummaries = await NotificationSummary.find({ created_by: req.user._id })
            .skip((page - 1) * page_size)
            .limit(page_size);

        res.json({
            data: notificationSummaries,
            total_pages: totalPages,
            total_documents: totalDocuments,
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
        });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});

router.get(
    "/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req, res) => {
        try {
            const notificationSummary = await NotificationSummary.findOne({ _id: req.params.id, created_by: req.user._id });
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.patch(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("unit_id")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((unit) => {
                if (unit.length === 0) {
                    throw new Error(`At least one Unit ID is required`);
                }
                return true;
            }),
        body("preference")
            .optional()
            .isArray()
            .notEmpty()
            .withMessage(() => `preference must be an array`)
            .custom((preferences) => {
                const validPreferences = ["daily", "weekly", "monthly"];
                if (!preferences.every((p) => validPreferences.includes(p))) {
                    throw new Error(`Invalid preference value(s) provided`);
                }
                return true;
            }),
        body("receivers")
            .optional()
            .isArray()
            .withMessage(() => `receivers must be an array`)
            .custom((receivers) => {
                if (receivers.length > 20) {
                    throw new Error("A maximum of 20 receivers are allowed");
                }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        body("is_enabled")
            .optional()
            .custom((value) => {
                if (value === 1 || value === 0) {
                    return true;
                }
                throw new Error("is_enabled must be 0 or 1");
            }),
        body("title")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
    ]),
    async (req, res) => {
        const { receivers, unit_id, preference, is_enabled, title } = req.body;

        const updateData = {};
        if (unit_id) updateData.unit_id = unit_id;
        if (preference) updateData.preference = preference;
        if (receivers) updateData.receivers = receivers;
        if (typeof is_enabled !== "undefined") updateData.is_enabled = is_enabled;
        if (title) updateData.title = title;

        /** Ensure user cannot subscribe to units not assigned to them */
        if (unit_id) {
            if (!userHasPermissions(req.user, [permissions.accessAllUnits])) {
                if (unit_id.filter((id) => id !== "all").some((id) => !req.user.allowed_units.find((u) => u.unit_id === id))) {
                    return res.status(403).json({ message: "Not allowed to subscribe units not assigned to you" });
                }
            }
        }

        if (receivers) {
            const getDomains = await EmailDomains.find({});
            const domains = getDomains.map((domain) => domain.domain);
            if (!req.user.email) {
                return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
            }
            const userEmailDomain = req.user.email.split("@")[1];
            if (!domains.includes(userEmailDomain)) {
                return res.status(400).json({ message: "User email domain is not allowed." });
            }
            const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
            const allReceiversValid = receivers.every((email) => {
                const receiverDomain = email.split("@")[1];
                return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
            });
            if (!allReceiversValid) {
                return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
            }
            const notificationSummary = await NotificationSummary.findById(req.params.id);
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Alert not found" });
            }
            //check if new addition is made in payload separate that out please for me
            const newReceivers = receivers.filter((receiver) => !notificationSummary.receivers.includes(receiver));
            const { email } = req.user;

            for (let receiver of newReceivers) {
                const token = generateUnsubscribeToken(receiver, notificationSummary._id);

                const payload = {
                    addBy: email || "Unknown",
                    vessel: notificationSummary.title || "Unknown",
                    preference: notificationSummary.preference.join("/") || "Unknown",
                    link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
                };
                const emailBody = SUMMARY_SUBSCRIPTION_EMAIL_CONTENT(
                    payload,
                    new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                );
                sendEmail({
                    to: receiver,
                    subject: "Notification Alert",
                    html: emailBody,
                });
            }
        }
        try {
            const notificationSummary = await NotificationSummary.findOneAndUpdate({ _id: req.params.id, created_by: req.user._id }, updateData, {
                new: true,
            });
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req, res) => {
        try {
            const notificationSummary = await NotificationSummary.findOneAndDelete({
                _id: req.params.id,
                created_by: req.user._id,
            });
            if (!notificationSummary) {
                return res.status(404).json({ message: "Notification Summary not found" });
            }
            res.json(notificationSummary);
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_NOTIFICATION_SUMMARIES),
    isAuthenticated,
    validateData.bind(this, [
        body("unit_id")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((unit) => {
                if (unit.length === 0) {
                    throw new Error(`At least one Unit ID is required`);
                }
                return true;
            }),
        body("preference")
            .notEmpty()
            .isArray()
            .withMessage(() => `preference must be an array`),
        body("receivers")
            .isArray()
            .optional()
            .withMessage(() => `receivers must be an array`)
            .custom((receivers) => {
                if (receivers.length > 20) {
                    throw new Error("A maximum of 20 receivers are allowed");
                }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        body("is_enabled")
            .optional()
            .custom((value) => {
                if (value === 1 || value === 0) {
                    return true;
                }
                throw new Error("is_enabled must be 0 or 1");
            }),
        body("title")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
    ]),
    async (req, res) => {
        try {
            const { unit_id, preference, receivers, is_enabled, title } = req.body;
            let notificationSummary;

            /** Ensure user cannot subscribe to units not assigned to them */
            if (!userHasPermissions(req.user, [permissions.accessAllUnits])) {
                if (unit_id.filter((id) => id !== "all").some((id) => !req.user.allowed_units.find((u) => u.unit_id === id))) {
                    return res.status(403).json({ message: "Not allowed to subscribe units not assigned to you" });
                }
            }

            if (receivers) {
                const getDomains = await EmailDomains.find({});
                const domains = getDomains.map((domain) => domain.domain);
                if (!req.user.email) {
                    return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
                }
                const userEmailDomain = req.user.email.split("@")[1];
                if (!domains.includes(userEmailDomain)) {
                    return res.status(400).json({ message: "User email domain is not allowed." });
                }
                const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
                const allReceiversValid = receivers.every((email) => {
                    const receiverDomain = email.split("@")[1];
                    return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
                });
                if (!allReceiversValid) {
                    return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
                }
            }
            if (is_enabled) {
                notificationSummary = await Notification.create({
                    unit_id,
                    preference,
                    receivers,
                    title,
                    is_enabled: is_enabled === 1,
                    created_by: req.user._id,
                });
            } else {
                notificationSummary = await NotificationSummary.create({
                    unit_id,
                    preference,
                    receivers,
                    title,
                    created_by: req.user._id,
                });
            }
            if (receivers) {
                if (receivers.length > 0 && notificationSummary._id) {
                    const { email } = req.user;
                    for (let receiver of receivers) {
                        const token = generateUnsubscribeToken(receiver, notificationSummary._id);
                        const payload = {
                            addBy: email || "Unknown",
                            vessel: notificationSummary.title || "Unknown",
                            preference: notificationSummary.preference.join("/") || "Unknown",
                            link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
                        };
                        const emailBody = SUMMARY_SUBSCRIPTION_EMAIL_CONTENT(
                            payload,
                            new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                        );
                        sendEmail({
                            to: receiver,
                            subject: "Notification Alert",
                            html: emailBody,
                        });
                    }
                }
            }

            res.status(201).json(notificationSummary);
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.get(
    "/unsubscribe/email",
    assignEndpointId.bind(this, endpointIds.UNSUBSCRIBE_NOTIFICATION_SUMMARIES),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        // decrypt jwt_token to get email and notification_summary id in it
        const { token } = req.query;
        const { notificationId, email } = jwt.verify(token, process.env.JWT_SECRET);

        try {
            const notificationAlert = await NotificationSummary.findById(notificationId);
            if (!notificationAlert) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Something Went Wrong&message=Notification Summary does not exist`,
                );
            }

            if (!notificationAlert.receivers.includes(email)) {
                const user = await User.findOne({ email });

                if (user && user._id.toString() === notificationAlert.created_by.toString()) {
                    await NotificationSummary.updateOne(
                        { _id: notificationId },
                        { $set: { is_enabled: false, updated_at: new Date().toISOString() } },
                    );

                    return res.redirect(
                        `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification summary emails `,
                    );
                }

                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=401&title=Email Removed&message=This email does not exist or already removed from notification alert`,
                );
            }

            const updateRes = await NotificationSummary.updateOne({ _id: notificationId }, { $pull: { receivers: email } });

            if (!updateRes.modifiedCount) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Email Removed&message=This email does not exist or already removed from notification summary`,
                );
            }

            return res.redirect(
                `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `,
            );
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

module.exports = router;
