// swagger.js (or inside app.js)
const swaggerJsDoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const fs = require("fs");

const swaggerOptions = {
    swaggerDefinition: {
        openapi: "3.0.0",
        info: {
            title: "Quartermaster API",
            version: "2.0.0",
            description: fs.readFileSync("modules/swagger_info.html", "utf-8"),
            contact: {
                name: "Quartermaster",
                email: "<EMAIL>",
            },
        },
        servers: [
            process.env.NODE_ENV === "dev" && {
                url: `http://localhost:${process.env.PORT}/api`,
                description: "Local API",
            },
            {
                url: "https://staging.quartermaster.us/api",
                description: "Staging API",
            },
            {
                url: "https://portal.quartermaster.us/api",
                description: "Production API",
            },
        ].filter((o) => o),
        tags: [
            {
                name: "Auth",
                description: "Authentication endpoint",
            },
            {
                name: "Regions",
                description: "Fetch regions",
            },
            {
                name: "Vessels",
                description: "Fetch vessel information",
            },
            {
                name: "Streams",
                description: "Get data regarding streams",
            },
            {
                name: "Vessel Locations",
                description: "Fetch vessel location data",
            },
            {
                name: "Artifacts",
                description: "Fetch vessel artifacts data",
            },
            {
                name: "Storage",
                description: "Fetch file urls",
            },
            {
                name: "Statistics",
                description: "Fetch aggregate statistics",
            },
        ],
    },
    apis: [
        "./routes/*.js",
        "./routes/v2/*.js", // this path includes the v2 routes
    ],
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);

const swaggerConfig = {
    customCss: ``,
    customSiteTitle: "Quartermaster API",
    swaggerOptions: {
        responseInterceptor: (response) => {
            if (response.url.includes("/auth") && response.status === 200) {
                const token = response.body.jwt_token;
                if (token) {
                    window.ui.preauthorizeApiKey("bearerAuth", token);
                }
            }
            return response;
        },
    },
};

module.exports = { swaggerUi, swaggerDocs, swaggerConfig };

// body {
//     background-color: #000;
// }

// .swagger-ui * {
// color: white !important;
// }

// .swagger-ui .scheme-container {
//     background-color: rgb(42, 42, 42);
// }

// .swagger-ui .topbar {
//     background-color: rgb(42, 42, 42);
// }

// .swagger-ui .info .title {
//     color: #61dafb;
// }

// .swagger-ui .opblock-summary {
//     background-color: rgb(42, 42, 42);
//     border-color: #61dafb;
// }

// .swagger-ui .opblock-summary-method {
//     background-color: rgb(42, 42, 42);
//     color: white;
// }
