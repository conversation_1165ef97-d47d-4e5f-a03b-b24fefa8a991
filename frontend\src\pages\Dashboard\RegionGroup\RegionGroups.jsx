import { Chip, CircularProgress, Grid, Typography, FormControl, Select, MenuItem, Pagination, alpha, Box, Menu } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { DataGrid } from "@mui/x-data-grid";
import dayjs from "dayjs";
import { userValues } from "../../../utils";
import { ArrowDropDown, SentimentVeryDissatisfied } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import UpdateRegionGroupModal from "./UpdateRegionGroupModal";
import DeleteRegionGroupModal from "./DeleteRegionGroupModal";
import CreateRegionGroupModal from "./CreateRegionGroupModal";
import useGroupRegions from "../../../hooks/GroupRegionHook.jsx";
import DeleteButton from "../../../components/DeleteButton";
import EditButton from "../../../components/EditButton";
import { useUser } from "../../../hooks/UserHook.jsx";

export default function RegionGroups({ searchQuery, vessels, managedVessels, showCreateModal, setShowCreateModal }) {
    const { isMobile, timezone } = useApp();
    const { user } = useUser();
    const { regions, fetchRegions } = useGroupRegions();

    const [regionGroups, setRegionGroups] = useState([]);
    const [filteredRegionGroups, setFilteredRegionGroups] = useState([]);

    const [updateRegionGroup, setUpdateRegionGroup] = useState();
    const [showUpdateModal, setShowUpdateModal] = useState(false);
    const [deleteRegionGroup, setDeleteRegionGroup] = useState();
    const [deletingRegionGroup, setDeletingRegionGroup] = useState();

    const [isLoading, setIsLoading] = useState(true);

    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    useEffect(() => {
        fetchRegionGroups()
            .then(() => setIsLoading(false))
            .catch(console.error);
    }, [regions, managedVessels]);

    useEffect(() => {
        let newFilteredRegionGroups = [];
        const query = searchQuery.trim().toLowerCase();
        if (query) {
            newFilteredRegionGroups = regionGroups.filter((regionGroup) => {
                return (
                    regionGroup.name.toLowerCase().match(query) ||
                    regionGroup.vessels.some((vessel) => vessel.name.toLowerCase().match(query)) ||
                    regionGroup.managedVessels.some((vessel) => vessel.name.toLowerCase().match(query)) ||
                    regionGroup.created_by.name.toLowerCase().match(query)
                );
            });
            setFilteredRegionGroups(newFilteredRegionGroups);
        } else {
            setFilteredRegionGroups(regionGroups);
        }
        setPage(1);
    }, [searchQuery, regionGroups]);

    const fetchRegionGroups = async () => {
        try {
            if (regions) {
                const regionGroups = regions.map((regionGroup, index) => {
                    const managedVesselsForGroup = [];
                    if (regionGroup.vessel_ids && regionGroup.vessel_ids.length > 0 && managedVessels.length > 0) {
                        regionGroup.vessel_ids.forEach((vesselId) => {
                            const vessel = managedVessels.find((v) => v.vessel_id === vesselId);
                            if (vessel) {
                                managedVesselsForGroup.push(vessel);
                            }
                        });
                    }

                    return {
                        ...regionGroup,
                        serial: index + 1,
                        managedVessels: managedVesselsForGroup,
                    };
                });
                setRegionGroups(regionGroups);
            } else {
                fetchRegions();
            }
        } catch (err) {
            console.error("An error occurred while fetching region groups on the Region Groups Page:", err);
        }
    };

    const handleOnSuccess = async (type = "create", data = null) => {
        if (type === "create") {
            const managedVesselsForGroup = [];
            if (data.vessel_ids && data.vessel_ids.length > 0 && managedVessels.length > 0) {
                data.vessel_ids.forEach((vesselId) => {
                    const vessel = managedVessels.find((v) => v.vessel_id === vesselId);
                    if (vessel) {
                        managedVesselsForGroup.push(vessel);
                    }
                });
            }

            const newRegionGroup = {
                ...data,
                managedVessels: managedVesselsForGroup,
            };

            setRegionGroups((prev) => [...prev, newRegionGroup].map((regionGroup, index) => ({ ...regionGroup, serial: index + 1 })));
            setShowCreateModal(false);
        } else if (type === "update") {
            const managedVesselsForGroup = [];
            if (data.vessel_ids && data.vessel_ids.length > 0 && managedVessels.length > 0) {
                data.vessel_ids.forEach((vesselId) => {
                    const vessel = managedVessels.find((v) => v.vessel_id === vesselId);
                    if (vessel) {
                        managedVesselsForGroup.push(vessel);
                    }
                });
            }

            const updatedRegionGroup = {
                ...data,
                managedVessels: managedVesselsForGroup,
            };

            setRegionGroups((prev) =>
                prev.map((regionGroup) => (regionGroup._id === data._id ? { ...updatedRegionGroup, serial: regionGroup.serial } : regionGroup)),
            );
            setShowUpdateModal(false);
        } else if (type === "delete") {
            setRegionGroups((prev) => prev.filter((regionGroup) => regionGroup._id !== data._id));
            setDeletingRegionGroup(false);
        }
    };

    // Custom Footer Component
    const CustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
        const startIndex = (page - 1) * rowsPerPage + 1;
        const endIndex = Math.min(page * rowsPerPage, totalRows);

        return (
            <Grid
                container
                justifyContent={{ sm: "space-between", xs: "center" }}
                alignItems={"center"}
                padding={"10px"}
                backgroundColor={(theme) => alpha(theme.palette.custom.offline, 0.08)}
                gap={2}
                sx={{
                    borderRadius: "5px",
                }}
            >
                <Grid padding={"10px 20px"} size="auto">
                    <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                        {`${startIndex} - ${endIndex} of ${totalRows}`}
                    </Typography>
                </Grid>
                <Grid size="auto">
                    <Pagination
                        count={Math.ceil(totalRows / rowsPerPage)}
                        page={page}
                        onChange={onPageChange}
                        shape="rounded"
                        siblingCount={isMobile ? 0 : 1}
                        boundaryCount={1}
                        sx={{
                            "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                                color: "#FFFFFF",
                                minHeight: "30px",
                                fontSize: isMobile ? "9px" : "14px",
                                borderRadius: "8px",
                                minWidth: "32px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: (theme) => alpha(theme.palette.custom.offline, 0.2),
                            },
                            "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                                color: "#FFFFFF",
                                backgroundColor: (theme) => theme.palette.custom.mainBlue,
                            },
                        }}
                    />
                </Grid>
                <Grid justifyContent="flex-end" display={"flex"} size="auto">
                    <FormControl variant="outlined">
                        <Select
                            value={rowsPerPage}
                            onChange={onRowsPerPageChange}
                            sx={{
                                "& .MuiOutlinedInput-notchedOutline": {
                                    border: "none",
                                },
                                "& .MuiSelect-select": {
                                    padding: "10px",
                                    fontSize: isMobile ? "12px" : "16px",
                                    backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                    borderRadius: "5px",
                                    color: "#FFFFFF",
                                    minWidth: isMobile ? 0 : "80px",
                                },
                            }}
                        >
                            {[5, 10, 20].map((size) => (
                                <MenuItem key={size} value={size}>
                                    {isMobile ? size : `${size} / Page`}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Grid>
            </Grid>
        );
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
    };

    /**
     * @type {Array<import("@mui/x-data-grid").GridColDef>}
     */
    const columns = [
        { field: "serial", headerName: "S.No", minWidth: 50 },
        { field: "name", headerName: "Name", minWidth: 300 },
        { field: "timezone", headerName: "Timezone", minWidth: 200, valueGetter: (value) => `UTC ${value}` },
        {
            field: "vessels",
            headerName: "SmartMasts",
            minWidth: 150,
            renderCell: (params) => {
                // eslint-disable-next-line react-hooks/rules-of-hooks
                const [anchorEl, setAnchorEl] = useState(null);

                const smartMasts = params.row.vessels;

                const handleClick = (event) => {
                    setAnchorEl(event.currentTarget);
                };
                const handleClose = () => {
                    setAnchorEl(null);
                };

                return (
                    <>
                        {smartMasts && smartMasts.length > 0 && (
                            <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                                <Chip
                                    sx={{
                                        paddingLeft: 0.5,
                                        paddingRight: 2,
                                        display: "flex",
                                        flexDirection: "row-reverse",
                                        width: "fit-content",
                                        minWidth: { xs: "100%", xl: "50%" },
                                        borderRadius: "5px",
                                        justifyContent: "space-between",
                                        cursor: "pointer",
                                    }}
                                    icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                                    label={`${smartMasts.length} unit${smartMasts.length > 1 ? "s" : ""}`}
                                    onClick={handleClick}
                                />
                                <Menu
                                    anchorEl={anchorEl}
                                    open={Boolean(anchorEl)}
                                    onClose={handleClose}
                                    sx={{
                                        width: "100%",
                                    }}
                                >
                                    {smartMasts.map((smartMast, index) => (
                                        <MenuItem key={index} onClick={handleClose}>
                                            {smartMast.name}
                                        </MenuItem>
                                    ))}
                                </Menu>
                            </Box>
                        )}
                    </>
                );
            },
        },
        {
            field: "managedVessels",
            headerName: "Managed Vessels",
            minWidth: 150,
            renderCell: (params) => {
                // eslint-disable-next-line react-hooks/rules-of-hooks
                const [anchorEl, setAnchorEl] = useState(null);

                const vessels = params.row.managedVessels || [];

                const handleClick = (event) => {
                    setAnchorEl(event.currentTarget);
                };
                const handleClose = () => {
                    setAnchorEl(null);
                };

                return (
                    <>
                        {vessels && vessels.length > 0 ? (
                            <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                                <Chip
                                    sx={{
                                        paddingLeft: 0.5,
                                        paddingRight: 2,
                                        display: "flex",
                                        flexDirection: "row-reverse",
                                        width: "fit-content",
                                        minWidth: { xs: "100%", xl: "50%" },
                                        borderRadius: "5px",
                                        justifyContent: "space-between",
                                        cursor: "pointer",
                                    }}
                                    icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                                    label={`${vessels.length} vessel${vessels.length > 1 ? "s" : ""}`}
                                    onClick={handleClick}
                                />
                                <Menu
                                    anchorEl={anchorEl}
                                    open={Boolean(anchorEl)}
                                    onClose={handleClose}
                                    sx={{
                                        width: "100%",
                                    }}
                                >
                                    {vessels.map((vessel, index) => (
                                        <MenuItem key={index} onClick={handleClose}>
                                            {vessel.name || `Vessel ${vessel.vessel_id}`}
                                        </MenuItem>
                                    ))}
                                </Menu>
                            </Box>
                        ) : (
                            <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                                <Typography variant="body2" sx={{ opacity: 0.6 }}>
                                    No vessels
                                </Typography>
                            </Box>
                        )}
                    </>
                );
            },
        },
        {
            field: "creation_timestamp",
            headerName: "Created On",
            minWidth: 150,
            valueGetter: (v) =>
                dayjs(v)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true, exclude_hours: true, exclude_minutes: true })),
        },
        { field: "created_by", headerName: "Created By", minWidth: 250, valueGetter: (value) => value.name || "" },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 300,
            renderCell: (params) => (
                <Grid container>
                    <Grid>
                        {deletingRegionGroup === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <Grid container gap={1}>
                                <Grid>
                                    <EditButton
                                        onClick={() => {
                                            setUpdateRegionGroup(params.row);
                                            setShowUpdateModal(true);
                                        }}
                                    />
                                </Grid>
                                <Grid>
                                    <DeleteButton onClick={() => setDeleteRegionGroup(params.row)} />
                                </Grid>
                            </Grid>
                        )}
                    </Grid>
                </Grid>
            ),
        },
    ];

    const columnsWithouFilters = useMemo(
        () => [
            ...columns.map((col) => ({
                ...col,
                filterable: false,
                sortable: false,
                resizable: false,
                disableColumnMenu: true,
                disableReorder: true,
                disableExport: true,
                flex: 1,
            })),
        ],
        [columns],
    );

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={filteredRegionGroups.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
                    columns={columnsWithouFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <CustomFooter
                                page={page}
                                rowsPerPage={rowsPerPage}
                                totalRows={filteredRegionGroups.length}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay: () => (
                            <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
                                <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: (theme) => theme.palette.custom.borderColor }} />
                                <Typography variant="h6" component="div" gutterBottom color={(theme) => theme.palette.custom.borderColor}>
                                    No data available
                                </Typography>
                            </Grid>
                        ),
                    }}
                />
            </Grid>
            <CreateRegionGroupModal
                showCreateModal={showCreateModal}
                setShowCreateModal={setShowCreateModal}
                vessels={vessels}
                managedVessels={managedVessels}
                onSuccess={(data) => handleOnSuccess("create", data)}
            />
            <UpdateRegionGroupModal
                regionGroup={updateRegionGroup}
                showUpdateModal={showUpdateModal}
                setShowUpdateModal={setShowUpdateModal}
                onSuccess={(data) => handleOnSuccess("update", data)}
                vessels={vessels}
                managedVessels={managedVessels}
            />
            <DeleteRegionGroupModal
                regionGroup={deleteRegionGroup}
                setRegionGroup={setDeleteRegionGroup}
                setDeleting={setDeletingRegionGroup}
                onSuccess={(data) => handleOnSuccess("delete", data)}
            />
        </Grid>
    );
}
