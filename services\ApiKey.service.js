const ApiEndpoint = require("../models/ApiEndpoint");
const ApiKey = require("../models/ApiKey");
const { isValidObjectId } = require("mongoose");

class ApiKeyService {
    async fetchAll() {
        const apiKeys = await ApiKey.find({ is_deleted: false });
        return apiKeys;
    }

    async findById({ id }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOne({
            _id: id,
            is_deleted: false,
        });

        if (!apiKey) return null;
        return apiKey;
    }

    async create({ description, email }) {
        const apiKey = await Api<PERSON>ey.create({ description, email });
        return await this.findById({ id: apiKey._id });
    }

    async update({ id, description, email }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const data = { description, email };
        Object.keys(data).forEach((key) => {
            if (data[key] === undefined) delete data[key];
        });

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, data, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateAllowedEndpoints({ id, allowed_endpoints }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiEndpoints = await ApiEndpoint.find();
        if (allowed_endpoints.some((e_id) => !apiEndpoints.find((e) => e.endpoint_id === e_id))) throw new Error("Invalid endpoint provided");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_endpoints }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateAllowedUnits({ id, allowed_units }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_units }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateRevocationStatus({ id, is_revoked }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_revoked }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async delete({ id }) {
        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_deleted: true }, { new: true });
        return apiKey !== null;
    }
}

const apiKeyService = new ApiKeyService();

module.exports = apiKeyService;
