const mongoose = require("mongoose");
const db = require("../modules/db");
const crypto = require("crypto");
// const ioEmitter = require('../modules/ioEmitter');

const apiKeySchema = new mongoose.Schema({
    api_key: { type: String, required: true, default: () => crypto.randomBytes(16).toString("hex"), unique: true },
    description: { type: String, required: true },
    email: { type: String, required: false },
    allowed_endpoints: { type: Array, required: true },
    is_deleted: { type: Boolean, required: true, default: false },
    is_revoked: { type: Boolean, required: true, default: false },
    requests: { type: Number, required: true, default: 0 },
    jwt_token: { type: String, required: false },
    allowed_units: { type: Array, required: true, default: [] },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

/** TODO: temporarily disabled due to an api endpoint spam resulting in continuous listener invocation */
// apiKeySchema.post('save', emitChangedEvent)
// apiKeySchema.post('findOneAndDelete', emitChangedEvent)
// apiKeySchema.post("findOneAndUpdate", emitChangedEvent);

// function emitChangedEvent(apiKey) {
//     ioEmitter.emit('notifyAll', { name: `apiKeys/changed`, data: apiKey.toObject() });
// }

const ApiKey = db.qm.model("ApiKey", apiKeySchema, "api_keys");

module.exports = ApiKey;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
