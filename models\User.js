const mongoose = require("mongoose");
const ioEmitter = require("../modules/ioEmitter");
const db = require("../modules/db");
const { defaultDateTimeFormat } = require("../utils/timezonesList");

const userSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String },
    username: { type: String },
    date_time_format: { type: String, required: false, default: defaultDateTimeFormat },
    use_MGRS: { type: Boolean, required: false, default: false },
    password: { type: String, required: true },
    jwt_tokens: { type: Array, required: false, default: [] },
    reset_password_token: { type: String, required: false, default: null },
    reset_password_expire: { type: Number, required: false, default: null },
    email_verification_enabled: { type: Boolean, required: true, default: false },
    email_verified_device_ids: { type: Array, required: false, default: [] },
    role_id: { type: Number, required: true },
    // region: { type: String, required: true, default: 'us-east-1' }, @depreated, remove from DB after merge into production
    deletable: { type: Boolean, required: true, default: true },
    is_deleted: { type: Boolean, required: true, default: false },
    allowed_units: { type: Array, required: true },
    organization_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

userSchema.index({ email: 1, username: 1 }, { unique: true });

userSchema.post("save", emitChangedEvent);

userSchema.post("findOneAndDelete", emitChangedEvent);

function emitChangedEvent(user) {
    ioEmitter.emit("notifyAll", { name: `users/changed`, data: user.toObject() });
}

const User = db.qm.model("User", userSchema);

module.exports = User;
module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
